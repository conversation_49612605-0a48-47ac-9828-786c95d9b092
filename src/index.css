@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #c7372f;
  --primary-dark: #a62b24;
  --secondary: #2563eb;
  --secondary-dark: #1d4ed8;
  --accent: #f59e0b;
  --accent-dark: #d97706;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50;
  }
}

@layer components {
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  
  .section {
    @apply py-16 md:py-24;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold text-center mb-12 relative;
  }
  
  .section-title::after {
    content: '';
    @apply absolute w-20 h-1 bg-primary left-1/2 -translate-x-1/2 -bottom-3;
  }
  
  .btn {
    @apply inline-flex items-center justify-center rounded-md px-5 py-2.5 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark focus-visible:ring-primary;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark focus-visible:ring-secondary;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-transparent hover:bg-gray-100 focus-visible:ring-gray-400;
  }
}
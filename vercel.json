{"version": 2, "builds": [{"src": "api/**/*.ts", "use": "@vercel/node"}, {"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/contact", "methods": ["POST", "OPTIONS"], "dest": "/api/contact.ts"}, {"src": "/api/test-webhook", "methods": ["GET", "OPTIONS"], "dest": "/api/test-webhook.ts"}, {"src": "/api/discord", "methods": ["POST", "OPTIONS"], "dest": "/api/discord.ts"}, {"src": "/api/simple-discord", "methods": ["POST", "OPTIONS"], "dest": "/api/simple-discord.ts"}, {"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/assets/(.*)", "dest": "/assets/$1"}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html", "status": 200}]}